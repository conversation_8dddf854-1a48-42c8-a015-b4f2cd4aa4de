// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVpnTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button clearResultsBtn;

  @NonNull
  public final Button runConnectionTestBtn;

  @NonNull
  public final Button runFallbackTestBtn;

  @NonNull
  public final Button runValidationTestBtn;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final TextView testResultsText;

  private ActivityVpnTestBinding(@NonNull LinearLayout rootView, @NonNull Button clearResultsBtn,
      @NonNull Button runConnectionTestBtn, @NonNull Button runFallbackTestBtn,
      @NonNull Button runValidationTestBtn, @NonNull ScrollView scrollView,
      @NonNull TextView testResultsText) {
    this.rootView = rootView;
    this.clearResultsBtn = clearResultsBtn;
    this.runConnectionTestBtn = runConnectionTestBtn;
    this.runFallbackTestBtn = runFallbackTestBtn;
    this.runValidationTestBtn = runValidationTestBtn;
    this.scrollView = scrollView;
    this.testResultsText = testResultsText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVpnTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVpnTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_vpn_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVpnTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.clear_results_btn;
      Button clearResultsBtn = ViewBindings.findChildViewById(rootView, id);
      if (clearResultsBtn == null) {
        break missingId;
      }

      id = R.id.run_connection_test_btn;
      Button runConnectionTestBtn = ViewBindings.findChildViewById(rootView, id);
      if (runConnectionTestBtn == null) {
        break missingId;
      }

      id = R.id.run_fallback_test_btn;
      Button runFallbackTestBtn = ViewBindings.findChildViewById(rootView, id);
      if (runFallbackTestBtn == null) {
        break missingId;
      }

      id = R.id.run_validation_test_btn;
      Button runValidationTestBtn = ViewBindings.findChildViewById(rootView, id);
      if (runValidationTestBtn == null) {
        break missingId;
      }

      id = R.id.scroll_view;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.test_results_text;
      TextView testResultsText = ViewBindings.findChildViewById(rootView, id);
      if (testResultsText == null) {
        break missingId;
      }

      return new ActivityVpnTestBinding((LinearLayout) rootView, clearResultsBtn,
          runConnectionTestBtn, runFallbackTestBtn, runValidationTestBtn, scrollView,
          testResultsText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
