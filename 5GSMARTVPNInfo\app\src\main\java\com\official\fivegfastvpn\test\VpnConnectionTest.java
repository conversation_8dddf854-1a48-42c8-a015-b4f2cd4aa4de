package com.official.fivegfastvpn.test;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.BroadcastReceiver;
import android.os.Handler;
import android.util.Log;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.official.fivegfastvpn.model.Server;
import de.blinkt.openvpn.OpenVpnApiV2;

/**
 * Test class to verify VPN connection fixes
 * This class tests the improved VPN connection logic and fallback mechanisms
 */
public class VpnConnectionTest {
    
    private static final String TAG = "VpnConnectionTest";
    private Context context;
    private boolean testPassed = false;
    private String testResult = "";
    
    // Test configuration
    private static final String TEST_VPN_CONFIG = 
        "client\n" +
        "dev tun\n" +
        "proto udp\n" +
        "remote sg-sng.prod.surfshark.com 1194\n" +
        "resolv-retry infinite\n" +
        "nobind\n" +
        "persist-key\n" +
        "persist-tun\n" +
        "remote-cert-tls server\n" +
        "auth SHA256\n" +
        "cipher AES-256-CBC\n" +
        "verb 3\n" +
        "mute 20\n" +
        "auth-nocache\n" +
        "script-security 2\n" +
        "fast-io\n" +
        "comp-lzo no\n" +
        "pull\n" +
        "route-delay 2\n" +
        "dhcp-option DNS *******\n" +
        "dhcp-option DNS *******\n" +
        "redirect-gateway def1 bypass-dhcp\n" +
        "keepalive 10 120\n" +
        "auth-user-pass";
    
    public VpnConnectionTest(Context context) {
        this.context = context;
    }
    
    /**
     * Run comprehensive VPN connection test
     */
    public void runVpnConnectionTest(TestCallback callback) {
        Log.d(TAG, "Starting VPN connection test");
        
        // Register broadcast receiver to monitor connection state
        BroadcastReceiver testReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String state = intent.getStringExtra("state");
                Log.d(TAG, "VPN state changed: " + state);
                
                if ("CONNECTED".equals(state)) {
                    testPassed = true;
                    testResult = "VPN connection established successfully";
                    
                    // Test traffic monitoring
                    testTrafficMonitoring(callback);
                    
                } else if ("DISCONNECTED".equals(state)) {
                    if (!testPassed) {
                        testResult = "VPN connection failed - disconnected immediately";
                        callback.onTestCompleted(false, testResult);
                    }
                }
            }
        };
        
        // Register receiver
        IntentFilter filter = new IntentFilter("connectionState");
        LocalBroadcastManager.getInstance(context).registerReceiver(testReceiver, filter);
        
        // Create test server
        Server testServer = new Server(
            "Test Server",
            "test_flag.png",
            TEST_VPN_CONFIG,
            "testuser",
            "testpass"
        );
        
        try {
            // Start VPN connection using V2 implementation
            Log.d(TAG, "Starting VPN with V2 implementation");
            OpenVpnApiV2.startVpn(context, testServer.getOvpn(), testServer.getCountry(),
                    testServer.getOvpnUserName(), testServer.getOvpnUserPassword());
            
            // Set timeout for test
            new Handler().postDelayed(() -> {
                if (!testPassed) {
                    testResult = "VPN connection test timed out";
                    callback.onTestCompleted(false, testResult);
                }
                
                // Cleanup
                try {
                    LocalBroadcastManager.getInstance(context).unregisterReceiver(testReceiver);
                } catch (Exception e) {
                    Log.w(TAG, "Error unregistering receiver", e);
                }
            }, 30000); // 30 second timeout
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting VPN test", e);
            testResult = "Failed to start VPN: " + e.getMessage();
            callback.onTestCompleted(false, testResult);
        }
    }
    
    /**
     * Test traffic monitoring functionality
     */
    private void testTrafficMonitoring(TestCallback callback) {
        Log.d(TAG, "Testing traffic monitoring");
        
        BroadcastReceiver trafficReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                long uploaded = intent.getLongExtra("uploaded", 0);
                long downloaded = intent.getLongExtra("downloaded", 0);
                
                Log.d(TAG, "Traffic stats - Up: " + uploaded + " B, Down: " + downloaded + " B");
                
                if (uploaded > 0 || downloaded > 0) {
                    testResult += " - Traffic monitoring working (Up: " + uploaded + " B, Down: " + downloaded + " B)";
                    callback.onTestCompleted(true, testResult);
                    
                    // Cleanup
                    try {
                        LocalBroadcastManager.getInstance(context).unregisterReceiver(this);
                    } catch (Exception e) {
                        Log.w(TAG, "Error unregistering traffic receiver", e);
                    }
                }
            }
        };
        
        // Register traffic receiver
        IntentFilter trafficFilter = new IntentFilter("trafficStats");
        LocalBroadcastManager.getInstance(context).registerReceiver(trafficReceiver, trafficFilter);
        
        // Set timeout for traffic test
        new Handler().postDelayed(() -> {
            testResult += " - Traffic monitoring test completed";
            callback.onTestCompleted(true, testResult);
            
            // Cleanup
            try {
                LocalBroadcastManager.getInstance(context).unregisterReceiver(trafficReceiver);
            } catch (Exception e) {
                Log.w(TAG, "Error unregistering traffic receiver", e);
            }
        }, 10000); // 10 second timeout for traffic test
    }
    
    /**
     * Test server configuration validation
     */
    public void testServerConfigValidation(TestCallback callback) {
        Log.d(TAG, "Testing server configuration validation");
        
        // Test valid configuration
        Server validServer = new Server(
            "Valid Server",
            "flag.png",
            "client\ndev tun\nproto udp\nremote test.server.com 1194",
            "user",
            "pass"
        );
        
        // Test invalid configuration
        Server invalidServer = new Server(
            "Invalid Server",
            "flag.png",
            "invalid config",
            "user",
            "pass"
        );
        
        try {
            // This should work
            OpenVpnApiV2.startVpn(context, validServer.getOvpn(), validServer.getCountry(),
                    validServer.getOvpnUserName(), validServer.getOvpnUserPassword());
            
            // Stop the connection
            OpenVpnApiV2.stopVpn(context);
            
            callback.onTestCompleted(true, "Server configuration validation test passed");
            
        } catch (Exception e) {
            Log.e(TAG, "Server validation test failed", e);
            callback.onTestCompleted(false, "Server validation test failed: " + e.getMessage());
        }
    }
    
    /**
     * Test fallback mechanism
     */
    public void testFallbackMechanism(TestCallback callback) {
        Log.d(TAG, "Testing fallback mechanism");
        
        // Create a server configuration that might trigger fallback
        Server fallbackServer = new Server(
            "Fallback Test Server",
            "flag.png",
            TEST_VPN_CONFIG,
            "fallbackuser",
            "fallbackpass"
        );
        
        BroadcastReceiver fallbackReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Log.d(TAG, "Fallback mechanism triggered");
                callback.onTestCompleted(true, "Fallback mechanism test passed - native OpenVPN started");
                
                // Cleanup
                try {
                    context.unregisterReceiver(this);
                } catch (Exception e) {
                    Log.w(TAG, "Error unregistering fallback receiver", e);
                }
            }
        };
        
        // Register fallback receiver
        IntentFilter fallbackFilter = new IntentFilter("START_NATIVE_OPENVPN");
        context.registerReceiver(fallbackReceiver, fallbackFilter);
        
        try {
            // This might trigger the fallback mechanism
            OpenVpnApiV2.startVpn(context, fallbackServer.getOvpn(), fallbackServer.getCountry(),
                    fallbackServer.getOvpnUserName(), fallbackServer.getOvpnUserPassword());
            
            // Set timeout
            new Handler().postDelayed(() -> {
                callback.onTestCompleted(true, "Fallback mechanism test completed");
                
                // Cleanup
                try {
                    context.unregisterReceiver(fallbackReceiver);
                } catch (Exception e) {
                    Log.w(TAG, "Error unregistering fallback receiver", e);
                }
            }, 15000); // 15 second timeout
            
        } catch (Exception e) {
            Log.e(TAG, "Fallback test failed", e);
            callback.onTestCompleted(false, "Fallback test failed: " + e.getMessage());
        }
    }
    
    /**
     * Callback interface for test results
     */
    public interface TestCallback {
        void onTestCompleted(boolean success, String result);
    }
}
