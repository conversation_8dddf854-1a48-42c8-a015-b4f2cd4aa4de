package com.official.fivegfastvpn.test;

import android.app.Activity;
import android.content.Intent;
import android.net.VpnService;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import com.official.fivegfastvpn.R;

/**
 * Test activity for VPN connection fixes
 * This activity provides a UI to run various VPN tests and view results
 */
public class VpnTestActivity extends Activity {
    
    private static final String TAG = "VpnTestActivity";
    private static final int VPN_PERMISSION_REQUEST = 1001;
    
    private TextView testResultsText;
    private ScrollView scrollView;
    private Button runConnectionTestBtn;
    private Button runValidationTestBtn;
    private Button runFallbackTestBtn;
    private Button clearResultsBtn;
    
    private VpnConnectionTest vpnTest;
    private StringBuilder testResults = new StringBuilder();
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_vpn_test);
        
        initViews();
        setupClickListeners();
        
        vpnTest = new VpnConnectionTest(this);
        
        appendTestResult("VPN Connection Test Activity Started");
        appendTestResult("Ready to run tests...\n");
    }
    
    private void initViews() {
        testResultsText = findViewById(R.id.test_results_text);
        scrollView = findViewById(R.id.scroll_view);
        runConnectionTestBtn = findViewById(R.id.run_connection_test_btn);
        runValidationTestBtn = findViewById(R.id.run_validation_test_btn);
        runFallbackTestBtn = findViewById(R.id.run_fallback_test_btn);
        clearResultsBtn = findViewById(R.id.clear_results_btn);
    }
    
    private void setupClickListeners() {
        runConnectionTestBtn.setOnClickListener(v -> {
            if (checkVpnPermission()) {
                runConnectionTest();
            }
        });
        
        runValidationTestBtn.setOnClickListener(v -> {
            if (checkVpnPermission()) {
                runValidationTest();
            }
        });
        
        runFallbackTestBtn.setOnClickListener(v -> {
            if (checkVpnPermission()) {
                runFallbackTest();
            }
        });
        
        clearResultsBtn.setOnClickListener(v -> {
            testResults.setLength(0);
            testResultsText.setText("");
            appendTestResult("Test results cleared\n");
        });
    }
    
    private boolean checkVpnPermission() {
        Intent intent = VpnService.prepare(this);
        if (intent != null) {
            appendTestResult("VPN permission required, requesting...");
            startActivityForResult(intent, VPN_PERMISSION_REQUEST);
            return false;
        }
        return true;
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == VPN_PERMISSION_REQUEST) {
            if (resultCode == RESULT_OK) {
                appendTestResult("VPN permission granted");
                Toast.makeText(this, "VPN permission granted. You can now run tests.", Toast.LENGTH_SHORT).show();
            } else {
                appendTestResult("VPN permission denied");
                Toast.makeText(this, "VPN permission is required to run tests", Toast.LENGTH_SHORT).show();
            }
        }
    }
    
    private void runConnectionTest() {
        appendTestResult("=== Starting VPN Connection Test ===");
        runConnectionTestBtn.setEnabled(false);
        
        vpnTest.runVpnConnectionTest(new VpnConnectionTest.TestCallback() {
            @Override
            public void onTestCompleted(boolean success, String result) {
                runOnUiThread(() -> {
                    appendTestResult("Connection Test Result: " + (success ? "PASSED" : "FAILED"));
                    appendTestResult("Details: " + result);
                    appendTestResult("=== Connection Test Completed ===\n");
                    runConnectionTestBtn.setEnabled(true);
                });
            }
        });
    }
    
    private void runValidationTest() {
        appendTestResult("=== Starting Server Validation Test ===");
        runValidationTestBtn.setEnabled(false);
        
        vpnTest.testServerConfigValidation(new VpnConnectionTest.TestCallback() {
            @Override
            public void onTestCompleted(boolean success, String result) {
                runOnUiThread(() -> {
                    appendTestResult("Validation Test Result: " + (success ? "PASSED" : "FAILED"));
                    appendTestResult("Details: " + result);
                    appendTestResult("=== Validation Test Completed ===\n");
                    runValidationTestBtn.setEnabled(true);
                });
            }
        });
    }
    
    private void runFallbackTest() {
        appendTestResult("=== Starting Fallback Mechanism Test ===");
        runFallbackTestBtn.setEnabled(false);
        
        vpnTest.testFallbackMechanism(new VpnConnectionTest.TestCallback() {
            @Override
            public void onTestCompleted(boolean success, String result) {
                runOnUiThread(() -> {
                    appendTestResult("Fallback Test Result: " + (success ? "PASSED" : "FAILED"));
                    appendTestResult("Details: " + result);
                    appendTestResult("=== Fallback Test Completed ===\n");
                    runFallbackTestBtn.setEnabled(true);
                });
            }
        });
    }
    
    private void appendTestResult(String result) {
        testResults.append(result).append("\n");
        testResultsText.setText(testResults.toString());
        
        // Scroll to bottom
        scrollView.post(() -> scrollView.fullScroll(View.FOCUS_DOWN));
        
        Log.d(TAG, result);
    }
}
