<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="VPN Connection Test Suite"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="This test suite verifies the VPN connection fixes and improvements."
        android:textSize="14sp"
        android:textColor="#666666"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- Test Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp">

        <Button
            android:id="@+id/run_connection_test_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Run VPN Connection Test"
            android:textColor="#ffffff"
            android:background="#4CAF50"
            android:layout_marginBottom="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/run_validation_test_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Run Server Validation Test"
            android:textColor="#ffffff"
            android:background="#2196F3"
            android:layout_marginBottom="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/run_fallback_test_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Run Fallback Mechanism Test"
            android:textColor="#ffffff"
            android:background="#FF9800"
            android:layout_marginBottom="8dp"
            android:padding="12dp" />

        <Button
            android:id="@+id/clear_results_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Clear Results"
            android:textColor="#ffffff"
            android:background="#f44336"
            android:padding="12dp" />

    </LinearLayout>

    <!-- Test Results -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Test Results:"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#ffffff"
        android:padding="12dp"
        android:scrollbars="vertical">

        <TextView
            android:id="@+id/test_results_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#333333"
            android:fontFamily="monospace"
            android:text="Ready to run tests..." />

    </ScrollView>

</LinearLayout>
